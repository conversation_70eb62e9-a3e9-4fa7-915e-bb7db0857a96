# AISuperHelper PRD文档

## 1. 项目概述

AISuperHelper 是一款基于浏览器拓展的AI对话增强工具，旨在提升用户在使用网页端AI对话功能时的体验。它解决了现有AI对话网站功能不完善、使用体验不佳的问题，同时提供了一种低成本的替代方案，让用户无需导入昂贵的API key即可享受类似CherryStudio的高级功能。

## 2. 目标用户

- 使用网页端AI对话功能的普通用户
- 希望提升对话体验的用户
- 需要管理多个AI对话账号的用户
- 希望快速查找和使用提示词的用户
- 希望在对话过程中保持高效和便捷的用户

## 3. 功能需求

### 3.1 多账号管理
- 提供丝滑的多账号管理功能，支持极速切换账号
- 用户可以添加、删除、编辑多个AI对话账号

### 3.2 Prompt管理
- 提供强大的Prompt管理功能，支持贴心的提示词输入预设
- 用户可以创建、编辑、删除提示词
- 支持将相似类型的提示词划归到同一个分组
- 提供常用语功能，方便快速输入提示词

### 3.3 可视化Rate Limits
- 提供可视化rate limits功能，用户可以时刻了解剩余对话次数

### 3.4 历史消息全局搜索
- 提供历史消息全局搜索功能，用户可以快速查找之前的对话内容

### 3.5 对话记录快速复制
- 提供对话记录快速复制功能，方便用户接续上下文
- 支持直接复制原始文本
- 支持AI总结后复制，用户可以编辑提示词和调详细程度档位

### 3.6 对话窗口缩略图
- 提供对话窗口缩略图功能，用户可以快速跳转到对话位置

## 4. 技术需求

- 基于浏览器拓展开发
- 支持主流浏览器（Chrome、Firefox、Edge等）
- 使用现代前端框架react开发
- 数据存储使用浏览器本地存储或云端存储



## 5. 其他细节

- 提供快捷键支持，方便用户快速操作
- 提供插件更新提醒功能，方便用户及时获取最新功能