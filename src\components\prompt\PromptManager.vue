<template>
  <div class="prompt-manager">
    <div class="toolbar">
      <input v-model="searchTerm" placeholder="搜索模板..." class="search-input">
      <button @click="openEditorForm()" class="add-button">新建模板</button>
    </div>

    <!-- 添加分类过滤 -->
    <div class="category-filter">
      <button 
        v-for="category in categories"
        :key="category"
        :class="{ active: activeCategory === category }"
        @click="activeCategory = category">
        {{ category }}
      </button>
      <button 
        @click="activeCategory = null"
        :class="{ active: activeCategory === null }">
        全部
      </button>
    </div>

    <div class="template-grid">
      <div 
        v-for="template in filteredTemplates" 
        :key="template.id"
        class="template-card" 
        @click="editTemplate(template)"
      >
        <h3>{{ template.name }}</h3>
        <p>{{ previewContent(template.content) }}</p>
        <div class="card-footer">
          <span class="frequency">使用 {{ template.useCount || 0 }} 次</span>
          <button @click.stop="useTemplate(template)" class="use-button">使用</button>
          <span class="category-tag">{{ template.category || '默认' }}</span>
        </div>
      </div>
    </div>

    <PromptEditorModal 
      v-if="showEditor" 
      :template="currentTemplate" 
      @save="saveTemplate"
      @close="closeEditor"
    />
  </div>
</template>
