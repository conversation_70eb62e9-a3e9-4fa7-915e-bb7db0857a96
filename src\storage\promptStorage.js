export async function loadTemplates() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['promptTemplates'], (result) => {
      resolve(result.promptTemplates || []);
    });
  });
}

export function saveTemplate(template) {
  chrome.storage.local.get(['promptTemplates'], async (result) => {
    const templates = result.promptTemplates || [];
    
    // 自动提取模板变量
    const variables = extractTemplateVariables(template.content);
    
    // 更新逻辑
    const index = templates.findIndex(t => t.id === template.id);
    if (index > -1) {
      // 保留已有的使用次数
      template.useCount = templates[index].useCount;
      template.variables = variables;
      templates[index] = template;
    } else {
      template.id = generateTemplateId();
      template.variables = variables;
      template.useCount = 0; // 初始化使用次数
      templates.push(template);
    }

    await chrome.storage.local.set({ promptTemplates: templates });
  });
}

// 自动提取{变量} 
function extractTemplateVariables(content) {
  const pattern = /{(\w+)}/g;
  const variables = [];
  let match;
  const uniqueNames = new Set();
  
  while ((match = pattern.exec(content)) !== null) {
    const varName = match[1];
    if (!uniqueNames.has(varName)) {
      variables.push({ 
        name: varName,
        type: 'text'
      });
      uniqueNames.add(varName);
    }
  }
  return variables;
}

// 生成唯一ID
function generateTemplateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
