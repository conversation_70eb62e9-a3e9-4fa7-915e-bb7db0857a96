// 计算使用次数
export function incrementUsageCount(templateId) {
  chrome.storage.local.get(['promptTemplates'], (result) => {
    const templates = result.promptTemplates || [];
    const index = templates.findIndex(t => t.id === templateId);
    if (index > -1) {
      const template = templates[index];
      template.useCount = (template.useCount || 0) + 1;
      templates[index] = template;
      chrome.storage.local.set({ promptTemplates: templates });
    }
  });
}

// 渲染带变量的模板
export function renderTemplate(content, variableValues) {
  let rendered = content;
  for (const [key, value] of Object.entries(variableValues)) {
    const regex = new RegExp(`{${key}}`, 'g');
    rendered = rendered.replace(regex, value);
  }
  return rendered;
}
