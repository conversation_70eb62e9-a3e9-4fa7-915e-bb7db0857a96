# AISuperHelper PRD文档
## 1. 软件需求分析
[An overview and core features of the software, clearly defining the product's goals and key functionalities.]
### 1.1 产品概述
[Elaborate on the product's requirements and the objectives it aims to achieve.]
### 1.2 核心功能
[Feature Description: Detail the key functions and characteristics that constitute the product's core value.]
[Feature Scope: Clearly define the scope and limitations of the functionalities included in each core feature to prevent scope creep during later stages.]
[Website Requirements: {{Website Requirements}}]
## 2.用户操作功能
[Detailed descriptions of user operation functions, including user stories and operational flows, to help clarify how users interact with the product.]
### 2.1 用户故事
[Describe how users will interact with the product and their expectations, using the format: "As a [user type], I want to [perform a certain action], so that [achieve a certain goal]."]
### 2.2 操作流程
[Detail the steps and processes users go through to complete specific tasks. Illustrate these with a flowchart in Mermaid format.]
## 3.Service Sitemap    
[Design of the overall service structure, including sitemap diagrams and a list of pages/screens, outlining the service's organization and main sections.]
### 3.1 网站结构
[Provide an overview of the service's architecture using a Mermaid diagram.]
### 3.2 页面列表
[Detail all major pages within the service.]
## 4.页面原型图
[Provide a high-fidelity prototype of the service's main pages using HTML]