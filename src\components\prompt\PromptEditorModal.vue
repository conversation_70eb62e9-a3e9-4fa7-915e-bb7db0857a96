<template>
  <div class="modal">
    <div class="modal-content">
      <h2>{{ template.id ? '编辑模板' : '新建模板' }}</h2>
      
      <label>模板名称
        <input type="text" v-model="editableTemplate.name">
      </label>
      
      <!-- 添加模板分类 -->
      <label>分类
        <select v-model="editableTemplate.category">
          <option v-for="category in categories" :key="category" :value="category">
            {{ category }}
          </option>
          <option value="">默认</option>
        </select>
      </label>
      
      <label>模板内容
        <textarea 
          v-model="editableTemplate.content" 
          rows="6" 
          placeholder="如:{role}请帮我..."
          @keyup="detectVariables"
        />
      </label>
      
      <!-- 变量配置区域 -->
      <div v-if="editableTemplate.variables.length" class="variables-section">
        <h3>输入参数</h3>
        <div v-for="(variable, index) in editableTemplate.variables" 
             :key="index" class="variable-row">
          <label>
            {{ variable.name }} 
            <input type="text" v-model="editableTemplate.variableValues[variable.name]">
          </label>
        </div>
      </div>
      
      <div class="button-group">
        <button @click="$emit('save', editableTemplate)">保存</button>
        <button class="cancel" @click="$emit('close')">取消</button>
      </div>
    </div>
  </div>
</template>
